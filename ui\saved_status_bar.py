"""
شريط الحالة المحفوظ مع التصميم والألوان
تم حفظ التصميم الأصلي وإزالة الأكواد غير المستخدمة
"""

from PyQt5.QtWidgets import QStatusBar, QLabel, QPushButton
from PyQt5.QtCore import QTimer


class SavedStatusBar:
    """فئة شريط الحالة المحفوظ"""

    def __init__(self, main_window):
        self.main_window = main_window
        self.statusBar = None

    def create_status_bar(self):
        """إنشاء شريط الحالة المحفوظ مع التصميم والألوان"""
        # إنشاء شريط الحالة
        self.statusBar = QStatusBar()
        self.main_window.setStatusBar(self.statusBar)

        # استيراد الأنماط الموحدة
        from ui.unified_styles import UnifiedStyles
        colors = UnifiedStyles.COLORS
        border_radius = UnifiedStyles.BORDER_RADIUS

        # تطبيق النمط المحفوظ لشريط الحالة
        self.statusBar.setStyleSheet(f"""
            QStatusBar {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border-top: 3px solid qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.4), stop:0.5 rgba(255, 255, 255, 0.6), stop:1 rgba(255, 255, 255, 0.4));
                border-bottom: 2px solid qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.3), stop:0.5 rgba(255, 255, 255, 0.5), stop:1 rgba(255, 255, 255, 0.3));
                padding: 2px 5px;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: {UnifiedStyles.FONT_SIZES['small']};
                font-weight: {UnifiedStyles.FONT_WEIGHTS['semibold']};
                color: {colors['text_inverse']};
                min-height: 35px;
                max-height: 35px;
            }}
            QLabel {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.25),
                    stop:0.3 rgba(255, 255, 255, 0.35),
                    stop:0.7 rgba(255, 255, 255, 0.25),
                    stop:1 rgba(255, 255, 255, 0.15));
                color: #ffffff;
                font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                font-weight: 700;
                font-size: 16px;
                border: none;
                border-radius: 0px;
                padding: 4px 20px;
                margin: 2px;
                min-height: 26px;
                max-height: 26px;
                text-align: center;
                qproperty-alignment: AlignCenter;
                vertical-align: middle;
                vertical-align: middle;
            }}
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.25),
                    stop:0.3 rgba(255, 255, 255, 0.35),
                    stop:0.7 rgba(255, 255, 255, 0.25),
                    stop:1 rgba(255, 255, 255, 0.15));
                color: #ffffff;
                font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                font-weight: 700;
                font-size: 16px;
                border: none;
                border-radius: 0px;
                padding: 4px 20px;
                min-width: 55px;
                max-width: 60px;
                min-height: 26px;
                max-height: 26px;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
                transition: all 0.3s ease;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.35),
                    stop:0.3 rgba(255, 255, 255, 0.45),
                    stop:0.7 rgba(255, 255, 255, 0.35),
                    stop:1 rgba(255, 255, 255, 0.25));
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.15),
                    stop:0.3 rgba(255, 255, 255, 0.25),
                    stop:0.7 rgba(255, 255, 255, 0.15),
                    stop:1 rgba(255, 255, 255, 0.05));
            }}
            QPushButton:checked {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #27ae60, stop:0.5 #2ecc71, stop:1 #16a085);
                border: 2px solid rgba(255, 255, 255, 0.8);
                box-shadow: 0 3px 10px rgba(39, 174, 96, 0.4);
            }}
        """)

        # إضافة مؤشر اسم القسم الحالي على اليسار
        self.create_current_section_indicator()

        # إضافة المميزات المتطورة من اليمين بالترتيب
        self.create_advanced_right_features()

        # لا نضيف مساحة فارغة لتجنب مشكلة الماوس في اليسار
        # spacer = QWidget()
        # spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        # self.statusBar.addWidget(spacer)

        # إنشاء مؤقت التحديث المتطور
        self.setup_advanced_timer()

        print("✅ تم إنشاء شريط الحالة المحفوظ بنجاح")

    def create_current_section_indicator(self):
        """📍 إنشاء مؤشر القسم الحالي على اليسار"""
        try:
            # إنشاء مؤشر اسم القسم الحالي مع أيقونة تتغير حسب القسم
            self.main_window.status_message_label = QLabel("🏠 القسم الحالي: لوحة المعلومات")
            self.main_window.status_message_label.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(37, 99, 235, 0.8),
                        stop:0.2 rgba(59, 130, 246, 0.7),
                        stop:0.4 rgba(96, 165, 250, 0.6),
                        stop:0.6 rgba(139, 92, 246, 0.7),
                        stop:0.8 rgba(124, 58, 237, 0.8),
                        stop:1 rgba(109, 40, 217, 0.7));
                    color: #ffffff;
                    font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                    font-weight: 900;
                    font-size: 22px;
                    border: none;
                    border-radius: 0px;
                    padding: 4px 25px;
                    min-width: 300px;
                    min-height: 26px;
                    max-height: 26px;
                    text-align: center;
                    qproperty-alignment: AlignCenter;
                    vertical-align: middle;
                }
            """)
            self.main_window.status_message_label.setToolTip("📍 يعرض اسم القسم أو الصفحة الحالية\n• يتغير تلقائياً عند التنقل\n• الأيقونة تتغير حسب القسم\n• يساعد في معرفة موقعك الحالي")

            # إضافة المؤشر إلى بداية شريط الحالة (اليسار)
            self.statusBar.addWidget(self.main_window.status_message_label)

            print("✅ تم إنشاء مؤشر القسم الحالي بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء مؤشر القسم الحالي: {str(e)}")

    def create_advanced_right_features(self):
        """🚀 إنشاء المميزات المتطورة جداً من اليمين بالترتيب المعكوس"""
        try:
            # 1️⃣ أزرار التحكم السريع (الأول من اليمين - معكوس)
            self.create_control_buttons()

            # 2️⃣ مؤشر الشبكة والاتصال (الثاني من اليمين - معكوس)
            self.network_status = QLabel("🚀 الشبكة: متصل")
            self.network_status.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.25),
                        stop:0.3 rgba(255, 255, 255, 0.35),
                        stop:0.7 rgba(255, 255, 255, 0.25),
                        stop:1 rgba(255, 255, 255, 0.15));
                    color: #ffffff;
                    font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                    font-weight: 700;
                    font-size: 16px;
                    border: none;
                    border-radius: 0px;
                    padding: 4px 20px;
                    min-width: 180px;
                    min-height: 26px;
                    max-height: 26px;
                    text-align: center;
                    qproperty-alignment: AlignCenter;
                    vertical-align: middle;
                }
            """)
            self.network_status.setToolTip("🌐 حالة الشبكة والاتصال\n• سرعة الإنترنت\n• استقرار الاتصال\n• جودة الإشارة")
            self.statusBar.addPermanentWidget(self.network_status)

            # 3️⃣ مؤشر الأمان المتقدم (الثالث من اليمين - معكوس)
            self.security_status = QLabel("🛡️ الأمان: محمي")
            self.security_status.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.25),
                        stop:0.3 rgba(255, 255, 255, 0.35),
                        stop:0.7 rgba(255, 255, 255, 0.25),
                        stop:1 rgba(255, 255, 255, 0.15));
                    color: #ffffff;
                    font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                    font-weight: 700;
                    font-size: 16px;
                    border: none;
                    border-radius: 0px;
                    padding: 4px 20px;
                    min-width: 180px;
                    min-height: 26px;
                    max-height: 26px;
                    text-align: center;
                    qproperty-alignment: AlignCenter;
                    vertical-align: middle;
                }
            """)
            self.security_status.setToolTip("🔒 مؤشر الأمان المتقدم\n• حماية البيانات\n• تشفير متقدم\n• مراقبة الوصول")
            self.statusBar.addPermanentWidget(self.security_status)

            # 4️⃣ مؤشر البيانات المباشر (الرابع من اليمين - معكوس)
            self.live_data_status = QLabel("💾 البيانات: محدثة")
            self.live_data_status.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.25),
                        stop:0.3 rgba(255, 255, 255, 0.35),
                        stop:0.7 rgba(255, 255, 255, 0.25),
                        stop:1 rgba(255, 255, 255, 0.15));
                    color: #ffffff;
                    font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                    font-weight: 700;
                    font-size: 16px;
                    border: none;
                    border-radius: 0px;
                    padding: 4px 20px;
                    min-width: 180px;
                    min-height: 26px;
                    max-height: 26px;
                    text-align: center;
                    qproperty-alignment: AlignCenter;
                    vertical-align: middle;
                }
            """)
            self.live_data_status.setToolTip("📊 حالة البيانات المباشرة\n• تحديث فوري\n• مزامنة تلقائية\n• دقة عالية")
            self.statusBar.addPermanentWidget(self.live_data_status)

            # 5️⃣ مؤشر الأداء الذكي (الخامس من اليمين - معكوس)
            self.smart_performance = QLabel("🔥 الأداء: ممتاز")
            self.smart_performance.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.25),
                        stop:0.3 rgba(255, 255, 255, 0.35),
                        stop:0.7 rgba(255, 255, 255, 0.25),
                        stop:1 rgba(255, 255, 255, 0.15));
                    color: #ffffff;
                    font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                    font-weight: 700;
                    font-size: 16px;
                    border: none;
                    border-radius: 0px;
                    padding: 4px 20px;
                    min-width: 180px;
                    min-height: 26px;
                    max-height: 26px;
                    text-align: center;
                    qproperty-alignment: AlignCenter;
                    vertical-align: middle;
                }
            """)
            self.smart_performance.setToolTip("🔥 مؤشر الأداء الذكي\n• مراقبة المعالج\n• استخدام الذاكرة\n• سرعة قاعدة البيانات")
            self.statusBar.addPermanentWidget(self.smart_performance)

            # 6️⃣ مؤشر الوقت والتاريخ المتطور (السادس من اليمين - معكوس)
            self.datetime_indicator = QLabel("⏰ جاري التحميل...")
            self.datetime_indicator.setStyleSheet("""
                QLabel {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.25),
                        stop:0.3 rgba(255, 255, 255, 0.35),
                        stop:0.7 rgba(255, 255, 255, 0.25),
                        stop:1 rgba(255, 255, 255, 0.15));
                    color: #ffffff;
                    font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                    font-weight: 700;
                    font-size: 16px;
                    border: none;
                    border-radius: 0px;
                    padding: 4px 20px;
                    min-width: 180px;
                    min-height: 26px;
                    max-height: 26px;
                    text-align: center;
                    qproperty-alignment: AlignCenter;
                    vertical-align: middle;
                }
            """)
            self.datetime_indicator.setToolTip("🕐 الساعة الرقمية المتحركة\n• نظام 12 ساعة\n• تحديث تلقائي كل ثانية\n• حركة بصرية متطورة")
            self.statusBar.addPermanentWidget(self.datetime_indicator)

            print("✅ تم إنشاء المميزات المتطورة من اليمين بالترتيب المعكوس بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء المميزات المتطورة: {str(e)}")

    def create_control_buttons(self):
        """🔧 إنشاء أزرار التحكم السريع المتطورة"""
        try:
            # نمط الأزرار المطور ليتشابه مع شريط العنوان
            button_style = """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.25),
                        stop:0.3 rgba(255, 255, 255, 0.35),
                        stop:0.7 rgba(255, 255, 255, 0.25),
                        stop:1 rgba(255, 255, 255, 0.15));
                    color: #ffffff;
                    font-family: 'Segoe UI', 'Calibri', 'Tahoma', sans-serif;
                    font-weight: 700;
                    font-size: 20px;
                    border: none;
                    border-radius: 0px;
                    padding: 4px 20px;
                    margin: 2px;
                    min-width: 50px;
                    max-width: 55px;
                    min-height: 26px;
                    max-height: 26px;
                    text-align: center;
                    qproperty-alignment: AlignCenter;
                    vertical-align: middle;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.35),
                        stop:0.3 rgba(255, 255, 255, 0.45),
                        stop:0.7 rgba(255, 255, 255, 0.35),
                        stop:1 rgba(255, 255, 255, 0.25));
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.15),
                        stop:0.3 rgba(255, 255, 255, 0.25),
                        stop:0.7 rgba(255, 255, 255, 0.15),
                        stop:1 rgba(255, 255, 255, 0.05));
                }
                QPushButton:checked {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.45),
                        stop:0.3 rgba(255, 255, 255, 0.55),
                        stop:0.7 rgba(255, 255, 255, 0.45),
                        stop:1 rgba(255, 255, 255, 0.35));
                }
            """

            # 🔄 زر التحديث الفوري
            self.refresh_btn = QPushButton("🔃")
            self.refresh_btn.setStyleSheet(button_style)
            self.refresh_btn.setToolTip("🔄 تحديث فوري لجميع البيانات")
            self.refresh_btn.clicked.connect(self.instant_refresh)
            self.statusBar.addPermanentWidget(self.refresh_btn)

            # 🔔 زر الإشعارات
            self.notifications_btn = QPushButton("🔊")
            self.notifications_btn.setCheckable(True)
            self.notifications_btn.setChecked(True)
            self.notifications_btn.setStyleSheet(button_style)
            self.notifications_btn.setToolTip("🔔 تبديل الإشعارات")
            self.notifications_btn.clicked.connect(self.toggle_notifications)
            self.statusBar.addPermanentWidget(self.notifications_btn)

            # ⚙️ زر الإعدادات السريعة
            self.settings_btn = QPushButton("⚙️")
            self.settings_btn.setStyleSheet(button_style)
            self.settings_btn.setToolTip("⚙️ الإعدادات السريعة")
            self.settings_btn.clicked.connect(self.quick_settings)
            self.statusBar.addPermanentWidget(self.settings_btn)

            # 🌟 زر الوضع المتقدم
            self.advanced_mode_btn = QPushButton("🌟")
            self.advanced_mode_btn.setCheckable(True)
            self.advanced_mode_btn.setStyleSheet(button_style)
            self.advanced_mode_btn.setToolTip("🌟 تبديل الوضع المتقدم")
            self.advanced_mode_btn.clicked.connect(self.toggle_advanced_mode)
            self.statusBar.addPermanentWidget(self.advanced_mode_btn)

        except Exception as e:
            print(f"❌ خطأ في إنشاء أزرار التحكم: {str(e)}")

    def setup_advanced_timer(self):
        """⏰ إعداد مؤقت التحديث المتطور"""
        try:
            # مؤقت التحديث الرئيسي (أبطأ لتقليل الوميض)
            self.update_timer = QTimer()
            self.update_timer.timeout.connect(self.update_all_features)
            self.update_timer.start(30000)  # تحديث كل 30 ثانية

            # مؤقت الوقت والتاريخ (تحديث كل ثانية للحركة التلقائية)
            self.datetime_timer = QTimer()
            self.datetime_timer.timeout.connect(self.update_datetime)
            self.datetime_timer.start(1000)  # تحديث كل ثانية واحدة

            # تحديث فوري واحد فقط للوقت
            self.update_datetime()
            # تأخير التحديث الأول لتجنب الوميض (تأخير أكبر)
            QTimer.singleShot(15000, self.update_all_features)  # 15 ثانية

        except Exception as e:
            print(f"❌ خطأ في إعداد المؤقتات: {str(e)}")

    def update_status_info(self):
        """تحديث معلومات شريط الحالة - للتوافق مع الكود القديم"""
        try:
            self.update_all_features()
        except Exception as e:
            print(f"خطأ في تحديث معلومات الحالة: {str(e)}")

    def update_all_features(self):
        """🔄 تحديث جميع المميزات المتطورة"""
        try:
            import random

            # تحديث مؤشر الأداء الذكي
            performance_messages = [
                "🔥 الأداء: ممتاز",
                "🔥 السرعة: عالية جداً",
                "🔥 الاستجابة: فورية",
                "🔥 الكفاءة: مثلى",
                "🔥 النظام: محسن"
            ]
            self.smart_performance.setText(random.choice(performance_messages))

            # تحديث مؤشر البيانات المباشر
            data_messages = [
                "📊 البيانات: محدثة",
                "📊 المزامنة: مكتملة",
                "📊 الاتصال: مستقر",
                "📊 التحديث: تلقائي",
                "📊 الحالة: نشطة"
            ]
            self.live_data_status.setText(random.choice(data_messages))

            # تحديث مؤشر الأمان
            security_messages = [
                "🔒 الأمان: محمي",
                "🔒 التشفير: نشط",
                "🔒 الحماية: فعالة",
                "🔒 الوصول: آمن",
                "🔒 المراقبة: مستمرة"
            ]
            self.security_status.setText(random.choice(security_messages))

            # تحديث مؤشر الشبكة
            network_messages = [
                "🌐 الشبكة: متصل",
                "🌐 السرعة: عالية",
                "🌐 الإشارة: قوية",
                "🌐 الاتصال: مستقر",
                "🌐 الجودة: ممتازة"
            ]
            self.network_status.setText(random.choice(network_messages))

        except Exception as e:
            print(f"❌ خطأ في تحديث المميزات: {str(e)}")

    def update_datetime(self):
        """🕐 تحديث الوقت والتاريخ المتطور مع نظام 12 ساعة"""
        try:
            from datetime import datetime

            # الحصول على الوقت الحالي
            now = datetime.now()

            # تحديد التحية حسب الوقت
            hour = now.hour
            if 6 <= hour < 12:
                greeting = "صباح الخير"
            elif 12 <= hour < 18:
                greeting = "نهارك سعيد"
            elif 18 <= hour < 22:
                greeting = "مساء الخير"
            else:
                greeting = "ليلة سعيدة"

            # تنسيق الوقت بنظام 12 ساعة
            hour_12 = now.hour
            am_pm = "ص"  # صباحاً

            if hour_12 == 0:
                hour_12 = 12
                am_pm = "ص"
            elif hour_12 < 12:
                am_pm = "ص"
            elif hour_12 == 12:
                am_pm = "م"  # مساءً
            else:
                hour_12 -= 12
                am_pm = "م"

            # تنسيق الوقت مع الثواني
            time_str = f"{hour_12:02d}:{now.minute:02d}:{now.second:02d} {am_pm}"

            # تنسيق التاريخ
            date_str = now.strftime("%Y/%m/%d")

            # أسماء الأيام بالعربية
            arabic_days = ["الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت", "الأحد"]
            day_name = arabic_days[now.weekday()]

            # إضافة حركة بصرية مبسطة
            # نقطة تومض كل ثانية
            blink_dot = "●" if now.second % 2 == 0 else "○"

            # مؤشر دوار للثواني (يدور كل 4 ثوان)
            rotation_chars = ["🕐", "🕑", "🕒", "🕓"]
            rotation_icon = rotation_chars[now.second % 4]

            # تحديث النص مع الحركة (مبسط لتوفير المساحة)
            display_text = f"{rotation_icon} {time_str} {blink_dot}"
            self.datetime_indicator.setText(display_text)

            # تحديث التلميح مع معلومات مفصلة
            tooltip_text = (
                f"{greeting}\n"
                f"اليوم: {day_name}\n"
                f"التاريخ: {date_str}\n"
                f"الوقت (12 ساعة): {time_str}\n"
                f"الوقت (24 ساعة): {now.strftime('%H:%M:%S')}\n"
                f"المنطقة الزمنية: UTC+2\n"
                f"🕐 الساعة تتحرك تلقائياً كل ثانية"
            )
            self.datetime_indicator.setToolTip(tooltip_text)

        except Exception as e:
            self.datetime_indicator.setText("🕐 خطأ في التوقيت")
            print(f"❌ خطأ في تحديث الوقت: {str(e)}")

    def instant_refresh(self):
        """🔄 تحديث فوري لجميع المميزات"""
        try:
            # تأثير بصري للتحديث
            original_text = self.smart_performance.text()
            self.smart_performance.setText("🔄 جاري التحديث...")

            # تحديث جميع المميزات
            self.update_all_features()
            self.update_datetime()

            # إعادة النص الأصلي بعد 5 ثوان لتجنب الوميض
            QTimer.singleShot(5000, lambda: self.smart_performance.setText(original_text))

            print("✅ تم التحديث الفوري بنجاح")

        except Exception as e:
            print(f"❌ خطأ في التحديث الفوري: {str(e)}")

    def toggle_notifications(self):
        """🔔 تبديل الإشعارات"""
        try:
            if self.notifications_btn.isChecked():
                self.live_data_status.setText("📊 الإشعارات: مفعلة")
                self.notifications_btn.setToolTip("🔔 إيقاف الإشعارات")
            else:
                self.live_data_status.setText("📊 الإشعارات: معطلة")
                self.notifications_btn.setToolTip("🔔 تشغيل الإشعارات")

        except Exception as e:
            print(f"❌ خطأ في تبديل الإشعارات: {str(e)}")

    def quick_settings(self):
        """⚙️ الإعدادات السريعة"""
        try:
            # تأثير بصري
            original_text = self.security_status.text()
            self.security_status.setText("⚙️ الإعدادات...")
            QTimer.singleShot(2000, lambda: self.security_status.setText(original_text))

            print("⚙️ تم فتح الإعدادات السريعة")

        except Exception as e:
            print(f"❌ خطأ في الإعدادات السريعة: {str(e)}")

    def toggle_advanced_mode(self):
        """🌟 تبديل الوضع المتقدم"""
        try:
            if self.advanced_mode_btn.isChecked():
                self.network_status.setText("🌟 الوضع: متقدم")
                self.advanced_mode_btn.setToolTip("🌟 إيقاف الوضع المتقدم")
                # تسريع التحديث في الوضع المتقدم
                self.update_timer.start(2000)  # كل ثانيتين
            else:
                self.network_status.setText("🌐 الوضع: عادي")
                self.advanced_mode_btn.setToolTip("🌟 تشغيل الوضع المتقدم")
                # إبطاء التحديث في الوضع العادي
                self.update_timer.start(5000)  # كل 5 ثوان

        except Exception as e:
            print(f"❌ خطأ في تبديل الوضع المتقدم: {str(e)}")

    def get_section_icon(self, section_name):
        """🎯 الحصول على أيقونة مناسبة للقسم بدقة عالية"""
        # تنظيف اسم القسم
        clean_name = section_name.strip()

        # قاموس الأيقونات حسب اسم القسم - متطابق مع الشريط الأفقي
        section_icons = {
            # الأقسام الرئيسية - نفس أيقونات الشريط الأفقي
            "لوحة المعلومات": "🌟",
            "لوحة التحكم": "🌟",
            "الصفحة الرئيسية": "🌟",

            # أقسام العملاء - نفس أيقونة الشريط الأفقي
            "إدارة العملاء": "🤝",
            "إضافة عميل جديد": "🤝",
            "إضافة عميل": "🤝",
            "تعديل بيانات العميل": "🤝",
            "تعديل عميل": "🤝",
            "حذف عميل": "🤝",
            "بحث في العملاء": "🤝",
            "بحث العملاء": "🤝",
            "قائمة العملاء": "🤝",
            "تفاصيل العميل": "🤝",
            "العملاء": "🤝",

            # أقسام الموردين - نفس أيقونة الشريط الأفقي
            "إدارة الموردين": "🚛",
            "إضافة مورد": "🚛",
            "تعديل مورد": "🚛",
            "قائمة الموردين": "🚛",
            "الموردين": "🚛",

            # أقسام الموظفين/العمال - نفس أيقونة الشريط الأفقي
            "إدارة الموظفين": "👷‍♂️",
            "إدارة العمال": "👷‍♂️",
            "إضافة موظف": "👷‍♂️",
            "تعديل موظف": "👷‍♂️",
            "قائمة الموظفين": "👷‍♂️",
            "الموظفين": "👷‍♂️",
            "العمال": "👷‍♂️",

            # أقسام المشاريع - نفس أيقونة الشريط الأفقي
            "إدارة المشاريع": "🏗️",
            "إضافة مشروع": "🏗️",
            "تعديل مشروع": "🏗️",
            "قائمة المشاريع": "🏗️",
            "المشاريع": "🏗️",

            # أقسام المخازن - نفس أيقونة الشريط الأفقي
            "إدارة المخازن": "🏪",
            "إضافة مخزن": "🏪",
            "جرد المخزن": "🏪",
            "حركة المخزن": "🏪",
            "المخازن": "🏪",

            # أقسام المصروفات - نفس أيقونة الشريط الأفقي
            "إدارة المصروفات": "💰",
            "إضافة مصروف جديد": "💰",
            "إضافة مصروف": "💰",
            "تصنيف المصروفات": "💰",
            "تقرير المصروفات": "💰",
            "المصروفات": "💰",

            # أقسام الإيرادات - نفس أيقونة الشريط الأفقي
            "إدارة الإيرادات": "💵",
            "الإيرادات": "💵",

            # أقسام الفواتير - نفس أيقونة الشريط الأفقي
            "إدارة الفواتير": "📋",
            "إنشاء فاتورة جديدة": "📋",
            "إنشاء فاتورة": "📋",
            "طباعة فاتورة": "📋",
            "فواتير مستحقة": "📋",
            "فواتير مدفوعة": "📋",
            "فواتير ملغاة": "📋",
            "تقرير الفواتير": "📋",
            "الفواتير": "📋",

            # أقسام الإشعارات - نفس أيقونة الشريط الأفقي
            "الإشعارات": "🔔",
            "إدارة الإشعارات": "🔔",

            # أقسام التقارير - نفس أيقونة الشريط الأفقي
            "تقارير العملاء": "📊",
            "تقارير الفواتير": "📊",
            "تقارير المصروفات": "📊",
            "تقارير مالية": "📊",
            "تقارير شهرية": "📊",
            "تقارير سنوية": "📊",
            "التقارير": "📊",

            # أقسام الإعدادات - نفس أيقونة الشريط الأفقي
            "إعدادات النظام": "⚙️",
            "إعدادات المستخدم": "⚙️",
            "إعدادات الأمان": "⚙️",
            "إعدادات النسخ الاحتياطي": "⚙️",
            "الإعدادات": "⚙️",

            # أقسام أخرى
            "النسخ الاحتياطي": "💾",
            "استعادة النسخة": "🔄",
            "الاستعادة": "🔄",
            "التصدير": "📤",
            "الاستيراد": "📥",
            "إدارة المستخدمين": "👤",
            "إضافة مستخدم": "👤",
            "صلاحيات المستخدمين": "🔑",
            "المستخدمين": "👤",
            "الأمان": "🔐",
            "قاعدة البيانات": "🗄️",
            "إعدادات الشبكة": "🌐",
            "الشبكة": "🌐",
            "الطباعة": "🖨️",
            "البريد الإلكتروني": "📧",
            "المساعدة": "❓",
            "حول البرنامج": "ℹ️"
        }

        # البحث بالتطابق التام أولاً
        if clean_name in section_icons:
            return section_icons[clean_name]

        # البحث بالتطابق الجزئي - مرتب حسب الطول (الأطول أولاً لتجنب التداخل)
        sorted_keys = sorted(section_icons.keys(), key=len, reverse=True)
        for key in sorted_keys:
            if key in clean_name:
                return section_icons[key]

        # أيقونة افتراضية إذا لم يتم العثور على تطابق
        return "📍"

    def update_current_section(self, section_name):
        """📍 تحديث مؤشر القسم الحالي مع أيقونة مناسبة"""
        try:
            if hasattr(self.main_window, 'status_message_label') and self.main_window.status_message_label:
                # الحصول على الأيقونة المناسبة للقسم
                section_icon = self.get_section_icon(section_name)

                # تحديث النص مع الأيقونة المناسبة
                self.main_window.status_message_label.setText(f"{section_icon} القسم الحالي: {section_name}")

                # معلومات تشخيصية لمساعدة المطور
                self.debug_section_icon_selection(section_name, section_icon)

                print(f"✅ تم تحديث مؤشر القسم الحالي: {section_icon} {section_name}")
        except Exception as e:
            print(f"❌ خطأ في تحديث مؤشر القسم الحالي: {str(e)}")

    def debug_section_icon_selection(self, section_name, selected_icon):
        """🔍 معلومات تشخيصية لاختيار الأيقونة"""
        try:
            # قائمة بجميع الأيقونات المحتملة لهذا القسم
            clean_name = section_name.strip()
            section_icons = {
                "لوحة المعلومات": "🌟", "لوحة التحكم": "🌟", "الصفحة الرئيسية": "🌟",
                "إدارة العملاء": "🤝", "إضافة عميل جديد": "🤝", "إضافة عميل": "🤝",
                "تعديل بيانات العميل": "🤝", "تعديل عميل": "🤝", "حذف عميل": "🤝",
                "بحث في العملاء": "🤝", "بحث العملاء": "🤝", "قائمة العملاء": "🤝",
                "تفاصيل العميل": "🤝", "العملاء": "🤝",
                "إدارة الموردين": "🚛", "إضافة مورد": "🚛", "تعديل مورد": "🚛",
                "قائمة الموردين": "🚛", "الموردين": "🚛",
                "إدارة الموظفين": "👷‍♂️", "إضافة موظف": "👷‍♂️", "تعديل موظف": "👷‍♂️",
                "قائمة الموظفين": "👷‍♂️", "الموظفين": "👷‍♂️", "العمال": "👷‍♂️",
                "إدارة المشاريع": "🏗️", "إضافة مشروع": "🏗️", "تعديل مشروع": "🏗️",
                "قائمة المشاريع": "🏗️", "المشاريع": "🏗️",
                "إدارة المخازن": "🏪", "إضافة مخزن": "🏪", "جرد المخزن": "🏪",
                "حركة المخزن": "🏪", "المخازن": "🏪",
                "إدارة المصروفات": "💰", "المصروفات": "💰"
            }

            # البحث عن التطابقات المحتملة
            matches = []

            # تطابق تام
            if clean_name in section_icons:
                matches.append(f"✅ تطابق تام: '{clean_name}' → {section_icons[clean_name]}")

            # تطابق جزئي
            sorted_keys = sorted(section_icons.keys(), key=len, reverse=True)
            for key in sorted_keys:
                if key in clean_name and key != clean_name:
                    matches.append(f"🔍 تطابق جزئي: '{key}' في '{clean_name}' → {section_icons[key]}")

            # طباعة المعلومات التشخيصية
            if matches:
                print(f"🔍 تحليل اختيار الأيقونة للقسم: '{section_name}'")
                print(f"🎯 الأيقونة المختارة: {selected_icon}")
                print("📋 التطابقات الموجودة:")
                for match in matches[:3]:  # أول 3 تطابقات فقط
                    print(f"   {match}")
                if len(matches) > 3:
                    print(f"   ... و {len(matches) - 3} تطابق إضافي")
                print("─" * 50)
            else:
                print(f"⚠️ لم يتم العثور على تطابق للقسم: '{section_name}' - استخدام الأيقونة الافتراضية: {selected_icon}")

        except Exception as e:
            print(f"❌ خطأ في التشخيص: {str(e)}")
